{"name": "thainyfrontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "i18n-js": "^4.5.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-datepicker": "^8.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.5", "vite": "^7.0.3"}}