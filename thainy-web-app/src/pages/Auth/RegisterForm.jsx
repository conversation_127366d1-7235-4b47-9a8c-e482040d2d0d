import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useDispatch, useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Upload, FileText, Image, Trash2 } from 'lucide-react';
import { registerDefaultValues, registerSchema } from '../../schemas/register/registerSchema';
import { fetchHospitalsThunk } from '../../features/hospitals/hospitalSlice';
import { registerUserThunk } from '../../features/register/registerSlice';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { toast } from "react-toastify";
import { useLanguage } from '@/contexts/LanguageContext';

export default function RegisterForm({ onCancel }) {
    const dispatch = useDispatch();
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const { t } = useLanguage();

    // Get state from Redux
    const { hospitals, loading: hospitalsLoading } = useSelector(state => state.hospitals);
    const { loading: registerLoading, error: registerError, success } = useSelector(state => state.register);

    const registerForm = useForm({
        resolver: zodResolver(registerSchema),
        defaultValues: registerDefaultValues
    });

    // Fetch hospitals when component mounts
    useEffect(() => {
        dispatch(fetchHospitalsThunk());
    }, []);

    const handleFileUpload = (event) => {
        const files = Array.from(event.target.files);
        const newFiles = files.map(file => ({
            id: Date.now() + Math.random(),
            name: file.name,
            type: file.type,
            file: file
        }));
        setUploadedFiles(prev => [...prev, ...newFiles]);
    };

    const removeFile = (fileId) => {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    };

    const onRegisterSubmit = async (data) => {
        try {
            await dispatch(registerUserThunk(data)).unwrap();
            
            toast.success("Registration successful!");

        } catch (error) {
            toast.error(`Registration failed.${error.message}`);
        }
    };
    return (
        <Form {...registerForm}>
            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)}>
                {/* Hospital Selection */}
                <FormField
                    control={registerForm.control}
                    name="hospital"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Hospital")}</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                    <SelectTrigger className="w-full bg-[#F0EFEB] border-0">
                                        <SelectValue placeholder={t.t("Select hospital")} />
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    {hospitalsLoading ? (
                                        <SelectItem value="loading" disabled>{t.t("Loading hospitals...")}</SelectItem>
                                    ) : hospitals.length > 0 ? (
                                        hospitals.map(hospital => (
                                            <SelectItem
                                                key={hospital.hospital_number}
                                                value={hospital.hospital_name || "N/A"}
                                            >
                                                {hospital.hospital_name
                                                    ? `${hospital.hospital_name} (${hospital.hospital_number})`
                                                    : `Hospital ${hospital.hospital_number}`}
                                            </SelectItem>
                                        ))
                                    ) : (
                                        <SelectItem value="no-hospitals" disabled>{t.t("No hospitals available")}</SelectItem>
                                    )}
                                </SelectContent>

                            </Select>
                            <FormMessage className="text-xs text-red-500 mt-1" />
                        </FormItem>
                    )}
                />

                {/* Role Selection */}
                <FormField
                    control={registerForm.control}
                    name="role"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Role")}</FormLabel>
                            <FormControl>
                                <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="flex flex-row space-x-6 flex-wrap"
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Leader" id="leader" />
                                        <Label htmlFor="leader" className="text-sm">{t.t("Leader")}</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Doctor" id="doctor" />
                                        <Label htmlFor="doctor" className="text-sm">{t.t("Doctor")}</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="Collector" id="collector" />
                                        <Label htmlFor="collector" className="text-sm">{t.t("Collector")}</Label>
                                    </div>
                                </RadioGroup>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Name Fields */}
                <div>
                    <Label className="text-sm font-medium text-gray-700 mb-3 block">{t.t("First name - Last name")}</Label>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <FormField
                            control={registerForm.control}
                            name="firstName"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormControl>
                                        <Input
                                            placeholder={t.t("Please enter your first name")}
                                            className="w-full bg-[#F0EFEB] border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={registerForm.control}
                            name="lastName"
                            render={({ field }) => (
                                <FormItem className="flex-1">
                                    <FormControl>
                                        <Input
                                            placeholder={t.t("Please enter your last name")}
                                            className="w-full bg-[#F0EFEB] border-0"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {/* Date of Birth and Telephone */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <FormField
                        control={registerForm.control}
                        name="dateOfBirth"
                        render={({ field }) => (
                            <FormItem className="flex-1">
                                <FormLabel className="text-sm font-medium !text-gray-700">
                                    {t.t("Date of Birth (B.E.)")}
                                </FormLabel>
                                <FormControl>
                                    <DatePicker
                                        selected={field.value ? new Date(field.value) : null}
                                        onChange={(date) => {
                                            if (date) {
                                                // Save ISO Gregorian for backend
                                                field.onChange(date.toISOString().split("T")[0]);
                                            } else {
                                                field.onChange("");
                                            }
                                        }}
                                        placeholderText={t.t("Select date")}
                                        dateFormat="dd/MM/yyyy"
                                        className="w-full bg-[#F0EFEB] border-0 rounded-md px-3 py-2"
                                        renderCustomHeader={({
                                            date,
                                            changeYear,
                                            changeMonth,
                                            decreaseMonth,
                                            increaseMonth,
                                            prevMonthButtonDisabled,
                                            nextMonthButtonDisabled,
                                        }) => {
                                            const currentYear = new Date().getFullYear();
                                            const years = Array.from({ length: 51 }, (_, i) => currentYear - i);
                                            // 51 values = current year + 50 years back

                                            const months = [
                                                "January", "February", "March", "April", "May", "June",
                                                "July", "August", "September", "October", "November", "December"
                                            ];

                                            return (
                                                <div className="flex justify-between items-center px-2 py-1">
                                                    <button onClick={decreaseMonth} disabled={prevMonthButtonDisabled}>{"<"}</button>

                                                    {/* Year dropdown with BE conversion */}
                                                    <select
                                                        value={date.getFullYear()}
                                                        onChange={(e) => changeYear(parseInt(e.target.value))}
                                                    >
                                                        {years.map((year) => (
                                                            <option key={year} value={year}>
                                                                {year + 543} {/* Show BE */}
                                                            </option>
                                                        ))}
                                                    </select>

                                                    {/* Month dropdown */}
                                                    <select
                                                        value={date.getMonth()}
                                                        onChange={(e) => changeMonth(parseInt(e.target.value))}
                                                    >
                                                        {months.map((month, index) => (
                                                            <option key={index} value={index}>
                                                                {month}
                                                            </option>
                                                        ))}
                                                    </select>

                                                    <button onClick={increaseMonth} disabled={nextMonthButtonDisabled}>{">"}</button>
                                                </div>
                                            );
                                        }}
                                    />


                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={registerForm.control}
                        name="telephone"
                        render={({ field }) => (
                            <FormItem className="flex-1">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Telephone")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="tel"
                                        placeholder={t.t("Please enter your Telephone")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Email */}
                <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Email")}</FormLabel>
                            <FormControl>
                                <Input
                                    type="email"
                                    placeholder={t.t("Please enter your email")}
                                    className="w-full bg-[#F0EFEB] border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Username */}
                <FormField
                    control={registerForm.control}
                    name="username"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Username")}</FormLabel>
                            <FormControl>
                                <Input
                                    placeholder={t.t("Please enter your username")}
                                    className="w-full bg-[#F0EFEB] border-0"
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Password Fields */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <FormField
                        control={registerForm.control}
                        name="password"
                        render={({ field }) => (
                            <FormItem className="flex-1">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Password")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder={t.t("Please enter your password")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={registerForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                            <FormItem className="flex-1">
                                <FormLabel className="text-sm font-medium !text-gray-700">{t.t("Confirm Password")}</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder={t.t("Please re-enter your password")}
                                        className="w-full bg-[#F0EFEB] border-0"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Password Requirements */}
                <div className="text-xs text-red-500">
                    <p>{t.t("Password Requirements")}</p>
                    <p>{t.t("The password must contain at least 1 uppercase letter, 1")}</p>
                    <p>{t.t("lowercase letter, and 1 number, with a length of 6-12")}</p>
                    <p>{t.t("characters.")}</p>
                </div>

                {/* Certificate Attachments */}
                <div className="rounded-lg border border-gray-200 shadow-sm bg-[#F0EFEB] ">
                    {/* Header Row */}
                    <div className="flex items-center justify-between  rounded-md bg-white shadow-md p-3">
                        <div>
                            <h3 className="text-sm font-semibold text-gray-800">{t.t("Certificate Attachments")}</h3>
                            <p className="text-xs text-gray-500">
                                {t.t("Number of files uploaded:")}{" "}
                                <span className="font-medium">{uploadedFiles.length} {t.t("files")}</span>
                            </p>
                        </div>

                        {/* File Upload Button */}
                        <label htmlFor="file-upload" className="cursor-pointer">
                            <div className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-md shadow hover:bg-gray-800 transition">
                                <Upload className="w-4 h-4" />
                                <span className="text-sm font-medium">{t.t("Select file")}</span>
                            </div>
                            <input
                                id="file-upload"
                                type="file"
                                multiple
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={handleFileUpload}
                                className="hidden"
                            />
                        </label>
                    </div>


                    <div className=" p-3 ">
                        {/* Uploaded Files List */}
                        {uploadedFiles.length > 0 && (
                            <div className=" ">
                                {uploadedFiles.map((file) => (
                                    <div
                                        key={file.id}
                                        className="flex items-center justify-between px-4 py-2 bg-gray-50 border border-gray-200" // removed rounded-md here
                                    >
                                        <div className="flex items-center gap-2">
                                            {file.type.includes("pdf") ? (
                                                <FileText className="w-4 h-4 text-red-500" />
                                            ) : (
                                                <Image className="w-4 h-4 text-blue-500" />
                                            )}
                                            <span className="text-sm text-gray-700 font-medium">{file.name}</span>
                                        </div>

                                        <button
                                            type="button"
                                            onClick={() => removeFile(file.id)}
                                            className="flex items-center gap-1 text-xs font-medium text-red-500 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                            {t.t("delete")}
                                        </button>
                                    </div>
                                ))}
                            </div>

                        )}
                    </div>
                </div>



                {/* Display error message if registration fails */}
                {registerError && (
                    <div className="text-red-500 text-sm text-center mb-4">
                        {registerError}
                    </div>
                )}

                {/* Display success message */}
                {success && (
                    <div className="text-green-500 text-sm text-center mb-4">
                        Registration successful! Please check your email for verification.
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <Button
                        type="button"
                        variant="outline"
                        className="flex-1 bg-gray-300 text-gray-700 border-0 hover:bg-gray-400"
                        onClick={onCancel}
                        disabled={registerLoading}
                    >
                        {t.t("Cancel")}
                    </Button>
                    <Button
                        type="submit"
                        className="flex-1"
                        disabled={registerLoading}
                        style={{ backgroundColor: '#5fb3b3' }}
                    >
                        {registerLoading ? t.t('Registering...') : t.t('Register')}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
