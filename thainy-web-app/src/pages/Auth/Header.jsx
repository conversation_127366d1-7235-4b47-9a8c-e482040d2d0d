import { useLanguage } from '@/contexts/LanguageContext';
import { LanguageToggleButton } from '@/components/ui/language-toggle';

export default function Header({ isLogin, setIsLogin }) {
    const { t } = useLanguage();

    return (
        <header className="flex w-full justify-center p-4 md:p-6 h-[196px]" style={{ backgroundColor: '#50A0A0' }}>
            {/* Centered max-width container */}
            <div className="flex w-full md:max-w-4xl items-center justify-between p-4 md:p-6">
                <div className="flex flex-col space-y-2">
                    {/* Row 1: Logo + Title */}
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                            <div className="w-6 h-6 bg-primary rounded-full"></div>
                        </div>
                        <h1 className="text-xl md:text-2xl font-bold text-white">
                            {t.t("ThaiNy Training System")}
                        </h1>
                    </div>

                    {/* Row 2: Login + Register */}
                    <div className="flex flex-wrap gap-4 pl-14">
                        <button
                            onClick={() => setIsLogin(true)}
                            className={`text-sm md:text-base ${isLogin ? "text-white font-semibold" : "text-white/80"
                                }`}
                        >
                            {t.t("Login")}
                        </button>
                        <button
                            onClick={() => setIsLogin(false)}
                            className={`text-sm md:text-base ${!isLogin
                                    ? "text-white font-semibold border-b-2 border-orange-300"
                                    : "text-white/80"
                                }`}
                        >
                            {t.t("Register")}
                        </button>
                    </div>
                </div>

                {/* Language Toggle */}
                <div className="flex items-center">
                    <LanguageToggleButton />
                </div>
            </div>
        </header>
    );
}
