import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Globe } from 'lucide-react';

export function LanguageToggle() {
    const { locale, updateLanguage } = useLanguage();

    const handleLanguageChange = (newLanguage) => {
        updateLanguage(newLanguage);
    };

    return (
        <div className="flex items-center gap-2">
            <Globe className="w-4 h-4 text-white" />
            <Select value={locale} onValueChange={handleLanguageChange}>
                <SelectTrigger className="w-auto min-w-[80px] bg-transparent border-white/30 text-white hover:bg-white/10">
                    <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="th">ไทย</SelectItem>
                </SelectContent>
            </Select>
        </div>
    );
}

// Alternative button-style toggle
export function LanguageToggleButton() {
    const { locale, updateLanguage } = useLanguage();

    const toggleLanguage = () => {
        const newLanguage = locale === 'en' ? 'th' : 'en';
        updateLanguage(newLanguage);
    };

    return (
        <Button
            variant="ghost"
            size="sm"
            onClick={toggleLanguage}
            className="text-white hover:bg-white/10 border border-white/30"
        >
            <Globe className="w-4 h-4 mr-2" />
            {locale === 'en' ? 'ไทย' : 'EN'}
        </Button>
    );
}
