import React, { createContext, useContext, useEffect, useState } from "react";
import { I18n } from "i18n-js";
import languageFile from "../i18n/translation.json";

const LanguageContext = createContext();
const i18n = new I18n();
i18n.enableFallback = true;
i18n.translations = {
    en: languageFile.en,
    th: languageFile.th,
};

const getSystemLanguage = () => {
    const lang = navigator.language || (navigator.languages && navigator.languages[0]) || "en";
    return lang.startsWith("th") ? "th" : "en";
};

export const LanguageProvider = ({ children }) => {
    const [language, setLanguage] = useState(() =>
        localStorage.getItem("preferredLanguage") || getSystemLanguage()
    );
    const [currentLanguage, setCurrentLanguage] = useState(
        language === "th" ? "ไทย" : "English"
    );
    const [forceRender, setForceRender] = useState(0);

    const loadTranslations = async () => {
        const isDevMode = import.meta.env.VITE_MODE === "dev";
        const translationApi = import.meta.env.VITE_TRANSLATIONS_API_URL;

        if (isDevMode && translationApi) {
            try {
                const response = await fetch(translationApi);
                const data = await response.json();
                i18n.translations = {
                    en: data.en,
                    th: data.th,
                };
            } catch (error) {
                console.error("Failed to fetch translations. Using local file.", error);
                i18n.translations = {
                    en: languageFile.en,
                    th: languageFile.th,
                };
            }
        } else {
            i18n.translations = {
                en: languageFile.en,
                th: languageFile.th,
            };
        }
    };

    const loadLanguagePreference = () => {
        const storedLanguage = localStorage.getItem("preferredLanguage");
        if (storedLanguage) {
            setLanguage(storedLanguage);
            setCurrentLanguage(storedLanguage === "th" ? "ไทย" : "English");
        } else {
            const systemLang = getSystemLanguage();
            localStorage.setItem("preferredLanguage", systemLang);
            setLanguage(systemLang);
            setCurrentLanguage(systemLang === "th" ? "ไทย" : "English");
        }
    };

    useEffect(() => {
        loadTranslations();
        loadLanguagePreference();
    }, []);

    useEffect(() => {
        i18n.locale = language;
        setForceRender(prev => prev + 1);
    }, [language]);

    const updateLanguage = (code) => {
        setLanguage(code);
        localStorage.setItem("preferredLanguage", code);
        setCurrentLanguage(code === "th" ? "ไทย" : "English");
    };

    return (
        <LanguageContext.Provider
            value={{
                locale: language,
                updateLanguage,
                t: i18n,
                currentLanguage,
                setCurrentLanguage,
            }}
        >
            {children}
        </LanguageContext.Provider>
    );
};

export const useLanguage = () => useContext(LanguageContext);

export default LanguageContext;